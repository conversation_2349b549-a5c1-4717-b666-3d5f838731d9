import mongoose from 'mongoose';

const answerSchema = new mongoose.Schema({
  userInitial: {
    type: String,
    required: true,
    maxLength: 1
  },
  answers: [{
    question: {
      type: String,
      required: true
    },
    selected: {
      type: String,
      required: true
    },
    score: {
      type: Number,
      required: true
    }
  }],
  totalScore: {
    type: Number,
    required: true
  },
  timestamp: {
    type: Date,
    default: Date.now
  }
});

const Answer = mongoose.model('Answer', answerSchema);

export default Answer;