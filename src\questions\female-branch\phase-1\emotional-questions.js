// Phase 1: Emotional and Questioning - Female Branch
// 6 questions focused on emotional topics and general questioning

const emotionalQuestions = [
  {
    id: "f1_1",
    question: {
      en: "How many times you fall in love",
      hi: "Aap kitni baar pyaar mein pade hain?",
    },
    options: [
      { text: { en: "just time", hi: "bs baar" }, score: 2, next: "next" },
      { text: { en: "shuu, its a secret", hi: "shuu, yeh secret hai" }, score: 1, next: "next" },
      { text: { en: "Never", hi: "Kabhi nahi" }, score: 3, next: "next" },
    ],
  },
  {
    id: "f1_2",
    question: {
      en: "i secretly love person other than him, but also him",
      hi: "mujhe ye pasand hai, pr secretly mujhe ek or pasand hai",
    },
    options: [
      { text: { en: "I think so", hi: "Haan" }, score: 2, next: "next" },
      { text: { en: "Its a secret, girls problem 😁", hi: "Nahi" }, score: 1, next: "next" },
      { text: { en: "I think im clear", hi: "Pata nahi" }, score: 3, next: "next" },
    ],
  },
  {
    id: "f1_3",
    question: {
      en: "Do you love to do public affection, with your partner?, eg: holding hands, kiss him, etc.",
      hi: "aapko public mein pyaar dikhana pasand karta hai?",
    },
    options: [
      { text: { en: "yeah, we loved it", hi: "haan, hume pasand hai" }, score: 2, next: "next" },
      { text: { en: "yeah ,i wanted to do it, but there are some reason we can't", hi: "kuch log jante hain, par maine private chahiye" }, score: 1, next: "next" },
      { text: { en: "No, we are super private", hi: "nahi, hum super private hain, kisi ko bhi nahi bataya" }, score: 3, next: "next" },
    ],
  },
  {
    id: "f1_4",
    question: {
      en: "Can you fight against you family for him?",
      hi: "Kya aap uske liye apne family k kilaf lad sakti hain?",
    },
    options: [
      { text: {en:"Yes", hi: "Haan"}, score: 2, next: "next" },
      { text: {en:"No", hi: "Nahi"}, score: 1, next: "next" },
      { text: {en:"Its all depend on the situation goin on", hi: "yeh sub situation pe depend karta hai"}, score: 3, next: "next" },
    ],
  },
  {
    id: "f1_5",
    question: {
      en: "what is your relationship status ?",
      hi: "Aapka relationship status kya hai?",
    },
    options: [
      { text: { en: "its mingle", hi: "Haan" }, score: 2, next: "next" },
      { text: { en: "situationship", hi: "Nahi" }, score: 1, next: "next" },
      { text: { en: "I wanna explore more, he he", hi: "Pata nahi" }, score: 3, next: "next" },
    ],
  },
  {
    id: "f1_6",
    question: {
      en: "What do you think is the most important aspect of a relationship?",
      hi: "Relationship ka sabse important aspect kya hai?",
    },
    options: [
      { text: { en: "emotional connection", hi: "emotional connection" }, score: 2, next: "next" },
      { text: { en: "physical attraction", hi: "physical attraction" }, score: 1, next: "next" },
      { text: { en: "communication and trust", hi: "communication and trust" }, score: 3, next: "next" },
      { text: { en: "both", hi: "both" }, score: 3, next: "next" },
    ],
  },
];

export default emotionalQuestions;
