// API base URL - change this to your backend URL
const API_URL = 'http://localhost:5000/api';

// Save quiz answers to MongoDB backend
const saveAnswers = async (userInitial, answers, totalScore) => {
  try {
    // Create the data object to send
    const data = {
      userInitial,
      answers,
      totalScore
    };
    
    // Send data to backend API
    const response = await fetch(`${API_URL}/answers`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    });
    
    if (!response.ok) {
      throw new Error('Failed to save answers');
    }
    
    const result = await response.json();
    console.log('Answers saved successfully:', result);
    return result;
  } catch (error) {
    console.error('Error saving answers:', error);
    
    // Fallback to localStorage if API call fails
    try {
      const existingData = JSON.parse(localStorage.getItem('quizAnswers') || '[]');
      const fallbackData = {
        userInitial,
        answers,
        totalScore,
        timestamp: new Date().toISOString()
      };
      existingData.push(fallbackData);
      localStorage.setItem('quizAnswers', JSON.stringify(existingData));
      console.log('Answers saved to localStorage as fallback');
    } catch (localError) {
      console.error('LocalStorage fallback failed:', localError);
    }
    
    return { success: false, error: error.message };
  }
};

// Get all saved answers
const getAllAnswers = async () => {
  try {
    const response = await fetch(`${API_URL}/answers`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch answers');
    }
    
    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('Error fetching answers:', error);
    
    // Fallback to localStorage if API call fails
    try {
      return JSON.parse(localStorage.getItem('quizAnswers') || '[]');
    } catch (localError) {
      console.error('LocalStorage fallback failed:', localError);
      return [];
    }
  }
};

// Get answers by user initial
const getAnswersByInitial = async (initial) => {
  try {
    const response = await fetch(`${API_URL}/answers/${initial}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch answers');
    }
    
    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('Error fetching answers by initial:', error);
    
    // Fallback to localStorage if API call fails
    try {
      const allAnswers = JSON.parse(localStorage.getItem('quizAnswers') || '[]');
      return allAnswers.filter(answer => answer.userInitial === initial);
    } catch (localError) {
      console.error('LocalStorage fallback failed:', localError);
      return [];
    }
  }
};

export { saveAnswers, getAllAnswers, getAnswersByInitial };