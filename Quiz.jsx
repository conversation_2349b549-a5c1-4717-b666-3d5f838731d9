import React, { useState } from "react";
import {
  genderQuestion,
  maleBranch,
  femaleBranch,
} from "./src/questions/index";
import Agreement from "./src/Agreement";
import HeroPage from "./src/components/HeroPage";
import QuizGame from "./src/components/QuizGame";

function shuffleArray(array) {
  return [...array].sort(() => Math.random() - 0.5);
}

export default function Quiz() {
  // App state management
  const [started, setStarted] = useState(false);
  const [userInitials, setUserInitials] = useState("");
  const [language, setLanguage] = useState("en");

  // Game flow states
  const [gameState, setGameState] = useState("gender"); // "gender", "hero", "quiz", "results"
  const [selectedGender, setSelectedGender] = useState(null);
  const [selectedPhase, setSelectedPhase] = useState(null);
  const [currentQuizData, setCurrentQuizData] = useState(null);

  // Quiz results
  const [quizResults, setQuizResults] = useState(null);

  // Phase titles for display
  const phaseTitles = {
    phase1: {
      en: "Phase 1: Emotional and Questioning",
      hi: "Phase 1: Bhavnatmak aur Sawal"
    },
    phase2: {
      en: "Phase 2: Your Experience Until Now",
      hi: "Phase 2: Aapka Ab Tak Ka Anubhav"
    },
    phase3: {
      en: "Phase 3: Will You Do It",
      hi: "Phase 3: Kya Aap Karenge"
    }
  };

  // Event handlers
  function handleStart(initials) {
    setUserInitials(initials);
    setStarted(true);
  }

  function handleGenderSelect(gender) {
    setSelectedGender(gender);
    setGameState("hero");
  }

  function handlePhaseSelect(phase) {
    setSelectedPhase(phase);
    const branch = selectedGender === "male" ? maleBranch : femaleBranch;
    setCurrentQuizData(branch[phase]);
    setGameState("quiz");
  }

  function handleQuizEnd(results) {
    setQuizResults(results);
    setGameState("results");
  }

  function handleBackToPhases() {
    setGameState("hero");
    setSelectedPhase(null);
    setCurrentQuizData(null);
  }

  function handleBackToGender() {
    setGameState("gender");
    setSelectedGender(null);
    setSelectedPhase(null);
    setCurrentQuizData(null);
    setQuizResults(null);
  }

  function toggleLanguage() {
    setLanguage((prev) => (prev === "en" ? "hi" : "en"));
  }

  // Render logic based on game state
  if (!started) {
    return <Agreement onStart={handleStart} />;
  }

  if (gameState === "gender") {
    return <GenderSelection />;
  }

  if (gameState === "hero") {
    return (
      <HeroPage
        selectedGender={selectedGender}
        onPhaseSelect={handlePhaseSelect}
        language={language}
        onLanguageToggle={toggleLanguage}
      />
    );
  }

  if (gameState === "quiz") {
    return (
      <QuizGame
        questions={currentQuizData}
        language={language}
        onLanguageToggle={toggleLanguage}
        onQuizEnd={handleQuizEnd}
        onBackToPhases={handleBackToPhases}
        phaseTitle={phaseTitles[selectedPhase]}
        userInitials={userInitials}
      />
    );
  }

  if (gameState === "results") {
    return <QuizResults />;
  }

  // Gender Selection Component
  function GenderSelection() {
    const [selectedOption, setSelectedOption] = useState(null);

    function handleOptionSelect(option) {
      setSelectedOption(option);
    }

    function handleNext() {
      if (!selectedOption) return;
      handleGenderSelect(selectedOption.text.en.toLowerCase());
    }

    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-6">
        <div className="max-w-2xl mx-auto">
          <div className="bg-white bg-opacity-10 backdrop-blur-lg rounded-2xl p-8 border border-white border-opacity-20">
            <div className="flex justify-between items-center mb-8">
              <h2 className="text-2xl font-bold text-white">{genderQuestion.question[language]}</h2>
              <button
                onClick={toggleLanguage}
                className="text-sm px-4 py-2 bg-white bg-opacity-20 text-white rounded-lg hover:bg-opacity-30 transition-all"
              >
                {language === "en" ? "हिंग्लिश" : "English"}
              </button>
            </div>

            <div className="space-y-4">
              {genderQuestion.options.map((option, idx) => (
                <button
                  key={idx}
                  onClick={() => handleOptionSelect(option)}
                  className={`block w-full text-left p-4 border-2 rounded-xl transition-all duration-300 ${
                    selectedOption === option
                      ? "bg-gradient-to-r from-pink-500 to-purple-500 text-white border-transparent shadow-lg transform scale-105"
                      : "bg-white bg-opacity-10 text-white border-white border-opacity-30 hover:bg-opacity-20 hover:border-opacity-50"
                  }`}
                >
                  <span className="text-lg">{option.text[language]}</span>
                </button>
              ))}
            </div>

            {selectedOption && (
              <button
                onClick={handleNext}
                className="mt-8 w-full bg-gradient-to-r from-green-500 to-emerald-500 text-white font-bold py-4 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                {language === "en" ? "Continue" : "Aage Badhiye"}
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Quiz Results Component
  function QuizResults() {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-6">
        <div className="max-w-2xl mx-auto">
          <div className="bg-white bg-opacity-10 backdrop-blur-lg rounded-2xl p-8 border border-white border-opacity-20 text-center">
            <h2 className="text-4xl font-bold text-white mb-6">
              {language === "en" ? "Quiz Finished!" : "Quiz Samapt!"}
            </h2>
            <p className="text-xl text-gray-300 mb-4">
              {language === "en" ? "Thank you" : "Dhanyavaad"}, {userInitials}!
            </p>
            <p className="text-2xl text-white mb-8">
              {language === "en" ? "Your total score is:" : "Aapka kul score hai:"}{" "}
              <span className="text-yellow-400 font-bold">{quizResults?.score || 0}</span>
            </p>

            <div className="space-y-4">
              <button
                onClick={handleBackToPhases}
                className="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white font-bold py-3 rounded-xl hover:from-blue-600 hover:to-purple-600 transition-all duration-300"
              >
                {language === "en" ? "Try Another Phase" : "Dusra Phase Try Kariye"}
              </button>

              <button
                onClick={handleBackToGender}
                className="w-full bg-gradient-to-r from-gray-500 to-gray-600 text-white font-bold py-3 rounded-xl hover:from-gray-600 hover:to-gray-700 transition-all duration-300"
              >
                {language === "en" ? "Start Over" : "Phir Se Shuru Kariye"}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Default return - should not reach here
  return null;
}
