import React, { useState } from "react";
import {
  genderQuestion,
  maleBranch,
  femaleBranch,
} from "./src/questions/index";
import Agreement from "./src/Agreement";
import HeroPage from "./src/components/HeroPage";
import QuizGame from "./src/components/QuizGame";

function shuffleArray(array) {
  return [...array].sort(() => Math.random() - 0.5);
}

export default function Quiz() {
  // App state management
  const [started, setStarted] = useState(false);
  const [userInitials, setUserInitials] = useState("");
  const [language, setLanguage] = useState("en");

  // Game flow states
  const [gameState, setGameState] = useState("gender"); // "gender", "instruction", "quiz", "results"
  const [selectedGender, setSelectedGender] = useState(null);
  const [currentPhase, setCurrentPhase] = useState(1); // Track current phase (1, 2, or 3)
  const [currentQuizData, setCurrentQuizData] = useState(null);

  // Quiz results - track all phases
  const [allPhaseResults, setAllPhaseResults] = useState([]);
  const [totalScore, setTotalScore] = useState(0);

  // Phase information for display
  const phaseInfo = {
    1: {
      title: {
        en: "Phase 1: Emotional and Questioning",
        hi: "Phase 1: Bhavnatmak aur Sawal"
      },
      description: {
        en: "Explore your emotional side and answer questions about feelings, relationships, and personal connections.",
        hi: "Apne bhavnatmak pehlu ko samjhiye aur rishte, feelings ke baare mein sawalon ka jawab dijiye."
      },
      instruction: {
        en: "You'll be asked 6 questions about your emotional experiences and perspectives. Answer honestly.",
        hi: "Aapse 6 sawal puchhe jayenge aapke bhavnatmak anubhav ke baare mein. Sachche jawab dijiye."
      }
    },
    2: {
      title: {
        en: "Phase 2: Your Experience Until Now",
        hi: "Phase 2: Aapka Ab Tak Ka Anubhav"
      },
      description: {
        en: "Reflect on your past experiences, personal history, and the journey that brought you here.",
        hi: "Apne purane anubhav, vyaktigat itihaas aur yahan tak ke safar par vichar kariye."
      },
      instruction: {
        en: "6 questions about your past experiences and personal background. Share your story.",
        hi: "Aapke purane anubhav aur vyaktigat background ke baare mein 6 sawal. Apni kahani batayiye."
      }
    },
    3: {
      title: {
        en: "Phase 3: Will You Do It",
        hi: "Phase 3: Kya Aap Karenge"
      },
      description: {
        en: "The final phase with more provocative questions about your willingness and desires.",
        hi: "Antim phase mein aapki ichha aur readiness ke baare mein kuch bold sawal."
      },
      instruction: {
        en: "6 bold questions that explore your boundaries and desires. Be honest about your feelings.",
        hi: "6 bold sawal jo aapki boundaries aur desires ko explore karte hain. Apne feelings ke baare mein honest rahiye."
      }
    }
  };

  // Event handlers
  function handleStart(initials) {
    setUserInitials(initials);
    setStarted(true);
  }

  function handleGenderSelect(gender) {
    setSelectedGender(gender);
    setCurrentPhase(1); // Start with phase 1
    setGameState("instruction");
  }

  function handleStartPhase() {
    const branch = selectedGender === "male" ? maleBranch : femaleBranch;
    const phaseKey = `phase${currentPhase}`;
    setCurrentQuizData(branch[phaseKey]);
    setGameState("quiz");
  }

  function handleQuizEnd(results) {
    // Add current phase results to all results
    const newPhaseResult = {
      phase: currentPhase,
      ...results
    };
    setAllPhaseResults(prev => [...prev, newPhaseResult]);
    setTotalScore(prev => prev + results.score);

    // Check if this was the last phase
    if (currentPhase >= 3) {
      setGameState("results");
    } else {
      // Move to next phase
      setCurrentPhase(prev => prev + 1);
      setGameState("instruction");
    }
  }

  function handleRestartGame() {
    setGameState("gender");
    setSelectedGender(null);
    setCurrentPhase(1);
    setCurrentQuizData(null);
    setAllPhaseResults([]);
    setTotalScore(0);
  }

  function toggleLanguage() {
    setLanguage((prev) => (prev === "en" ? "hi" : "en"));
  }

  // Render logic based on game state
  if (!started) {
    return <Agreement onStart={handleStart} />;
  }

  if (gameState === "gender") {
    return <GenderSelection />;
  }

  if (gameState === "instruction") {
    return <PhaseInstruction />;
  }

  if (gameState === "quiz") {
    return (
      <QuizGame
        questions={currentQuizData}
        language={language}
        onLanguageToggle={toggleLanguage}
        onQuizEnd={handleQuizEnd}
        phaseTitle={phaseInfo[currentPhase].title}
        userInitials={userInitials}
        currentPhase={currentPhase}
      />
    );
  }

  if (gameState === "results") {
    return <QuizResults />;
  }

  // Gender Selection Component
  function GenderSelection() {
    const [selectedOption, setSelectedOption] = useState(null);

    function handleOptionSelect(option) {
      setSelectedOption(option);
    }

    function handleNext() {
      if (!selectedOption) return;
      handleGenderSelect(selectedOption.text.en.toLowerCase());
    }

    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 p-6">
        <div className="max-w-2xl mx-auto">
          <div className="bg-black bg-opacity-60 backdrop-blur-lg rounded-2xl p-8 border border-red-500 border-opacity-30">
            <div className="flex justify-between items-center mb-8">
              <h2 className="text-2xl font-bold text-red-400">{genderQuestion.question[language]}</h2>
              <button
                onClick={toggleLanguage}
                className="text-sm px-4 py-2 bg-red-900 bg-opacity-50 text-red-300 rounded-lg hover:bg-opacity-70 transition-all"
              >
                {language === "en" ? "हिंग्लिश" : "English"}
              </button>
            </div>

            <div className="space-y-4">
              {genderQuestion.options.map((option, idx) => (
                <button
                  key={idx}
                  onClick={() => handleOptionSelect(option)}
                  className={`block w-full text-left p-4 border-2 rounded-xl transition-all duration-300 ${
                    selectedOption === option
                      ? "bg-gradient-to-r from-red-600 to-red-800 text-white border-red-400 shadow-lg transform scale-105"
                      : "bg-black bg-opacity-40 text-red-300 border-red-500 border-opacity-30 hover:bg-opacity-60 hover:border-opacity-50"
                  }`}
                >
                  <span className="text-lg">{option.text[language]}</span>
                </button>
              ))}
            </div>

            {selectedOption && (
              <button
                onClick={handleNext}
                className="mt-8 w-full bg-gradient-to-r from-red-600 to-red-800 text-white font-bold py-4 rounded-xl hover:from-red-700 hover:to-red-900 transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                {language === "en" ? "Continue" : "Aage Badhiye"}
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Phase Instruction Component
  function PhaseInstruction() {
    const currentPhaseInfo = phaseInfo[currentPhase];

    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 p-6">
        <div className="max-w-3xl mx-auto">
          <div className="bg-black bg-opacity-60 backdrop-blur-lg rounded-2xl p-8 border border-red-500 border-opacity-30">
            <div className="flex justify-between items-center mb-8">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gradient-to-r from-red-600 to-red-800 rounded-full flex items-center justify-center mr-4">
                  <span className="text-2xl font-bold text-white">{currentPhase}</span>
                </div>
                <h1 className="text-3xl font-bold text-red-400">
                  {currentPhaseInfo.title[language]}
                </h1>
              </div>
              <button
                onClick={toggleLanguage}
                className="text-sm px-4 py-2 bg-red-900 bg-opacity-50 text-red-300 rounded-lg hover:bg-opacity-70 transition-all"
              >
                {language === "en" ? "हिंग्लिश" : "English"}
              </button>
            </div>

            <div className="text-center mb-8">
              <p className="text-xl text-red-300 mb-6 leading-relaxed">
                {language === "en" ? `Welcome ${userInitials}!` : `Swagat hai ${userInitials}!`}
              </p>

              <div className="bg-red-900 bg-opacity-20 rounded-xl p-6 mb-6">
                <p className="text-lg text-gray-300 mb-4 leading-relaxed">
                  {currentPhaseInfo.description[language]}
                </p>
                <p className="text-red-400 font-medium">
                  {currentPhaseInfo.instruction[language]}
                </p>
              </div>
            </div>

            <button
              onClick={handleStartPhase}
              className="w-full bg-gradient-to-r from-red-600 to-red-800 text-white font-bold py-4 rounded-xl hover:from-red-700 hover:to-red-900 transition-all duration-300 transform hover:scale-105 shadow-lg text-xl"
            >
              {language === "en" ? "Enter Phase" : "Phase Mein Jaiye"}
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Quiz Results Component
  function QuizResults() {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 p-6">
        <div className="max-w-3xl mx-auto">
          <div className="bg-black bg-opacity-60 backdrop-blur-lg rounded-2xl p-8 border border-red-500 border-opacity-30 text-center">
            <h2 className="text-4xl font-bold text-red-400 mb-6">
              {language === "en" ? "All Phases Complete!" : "Sabhi Phases Samapt!"}
            </h2>
            <p className="text-xl text-red-300 mb-6">
              {language === "en" ? "Thank you" : "Dhanyavaad"}, {userInitials}!
            </p>

            {/* Phase-wise results */}
            <div className="mb-8">
              <h3 className="text-2xl font-bold text-red-400 mb-4">
                {language === "en" ? "Your Journey:" : "Aapka Safar:"}
              </h3>
              <div className="space-y-3">
                {allPhaseResults.map((result, index) => (
                  <div key={index} className="bg-red-900 bg-opacity-20 rounded-lg p-4">
                    <div className="flex justify-between items-center">
                      <span className="text-red-300">
                        {language === "en" ? `Phase ${result.phase}` : `Phase ${result.phase}`}
                      </span>
                      <span className="text-red-400 font-bold">
                        {result.score} {language === "en" ? "points" : "points"}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <p className="text-3xl text-white mb-8">
              {language === "en" ? "Total Score:" : "Kul Score:"}{" "}
              <span className="text-red-400 font-bold">{totalScore}</span>
            </p>

            <div className="space-y-4">
              <button
                onClick={handleRestartGame}
                className="w-full bg-gradient-to-r from-red-600 to-red-800 text-white font-bold py-4 rounded-xl hover:from-red-700 hover:to-red-900 transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                {language === "en" ? "Start New Game" : "Naya Game Shuru Kariye"}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Default return - should not reach here
  return null;
}
