import React, { useState } from "react";
import {
  genderQuestion,
  maleBranch,
  femaleBranch,
} from "./src/questions/index";
import Agreement from "./src/Agreement";
import QuizGame from "./src/components/QuizGame";
import { calculatePersonality, getPersonalityResult } from "./src/utils/personalityScoring";

export default function Quiz() {
  // App state management
  const [started, setStarted] = useState(false);
  const [userInitials, setUserInitials] = useState("");
  const [language, setLanguage] = useState("en");

  // Game flow states
  const [gameState, setGameState] = useState("gender"); // "gender", "instruction", "quiz", "results"
  const [selectedGender, setSelectedGender] = useState(null);
  const [currentPhase, setCurrentPhase] = useState(1); // Track current phase (1, 2, or 3)
  const [currentQuizData, setCurrentQuizData] = useState(null);

  // Quiz results - track all phases
  const [allPhaseResults, setAllPhaseResults] = useState([]);
  const [totalScore, setTotalScore] = useState(0);

  // Phase information for display
  const phaseInfo = {
    1: {
      title: {
        en: "Phase 1: Emotional and Questioning",
        hi: "Phase 1: Bhavnatmak aur Sawal"
      },
      description: {
        en: "Explore your emotional side and answer questions about feelings, relationships, and personal connections.",
        hi: "Apne bhavnatmak pehlu ko samjhiye aur rishte, feelings ke baare mein sawalon ka jawab dijiye."
      },
      instruction: {
        en: "You'll be asked 6 questions about your emotional experiences and perspectives. Answer honestly.",
        hi: "Aapse 6 sawal puchhe jayenge aapke bhavnatmak anubhav ke baare mein. Sachche jawab dijiye."
      }
    },
    2: {
      title: {
        en: "Phase 2: Your Experience Until Now",
        hi: "Phase 2: Aapka Ab Tak Ka Anubhav"
      },
      description: {
        en: "Reflect on your past experiences, personal history, and the journey that brought you here.",
        hi: "Apne purane anubhav, vyaktigat itihaas aur yahan tak ke safar par vichar kariye."
      },
      instruction: {
        en: "6 questions about your past experiences and personal background. Share your story.",
        hi: "Aapke purane anubhav aur vyaktigat background ke baare mein 6 sawal. Apni kahani batayiye."
      }
    },
    3: {
      title: {
        en: "Phase 3: Will You Do It",
        hi: "Phase 3: Kya Aap Karenge"
      },
      description: {
        en: "The final phase with more provocative questions about your willingness and desires.",
        hi: "Antim phase mein aapki ichha aur readiness ke baare mein kuch bold sawal."
      },
      instruction: {
        en: "6 bold questions that explore your boundaries and desires. Be honest about your feelings.",
        hi: "6 bold sawal jo aapki boundaries aur desires ko explore karte hain. Apne feelings ke baare mein honest rahiye."
      }
    }
  };

  // Event handlers
  function handleStart(initials) {
    setUserInitials(initials);
    setStarted(true);
  }

  function handleGenderSelect(gender) {
    setSelectedGender(gender);
    setCurrentPhase(1); // Start with phase 1
    setGameState("instruction");
  }

  function handleStartPhase() {
    const branch = selectedGender === "male" ? maleBranch : femaleBranch;
    const phaseKey = `phase${currentPhase}`;
    setCurrentQuizData(branch[phaseKey]);
    setGameState("quiz");
  }

  function handleQuizEnd(results) {
    // Add current phase results to all results
    const newPhaseResult = {
      phase: currentPhase,
      ...results
    };
    const updatedResults = [...allPhaseResults, newPhaseResult];
    setAllPhaseResults(updatedResults);
    setTotalScore(prev => prev + results.score);

    // Check if this was the last phase
    if (currentPhase >= 3) {
      setGameState("results");
    } else {
      // Move to next phase
      setCurrentPhase(prev => prev + 1);
      setGameState("instruction");
    }
  }

  function handleRestartGame() {
    setGameState("gender");
    setSelectedGender(null);
    setCurrentPhase(1);
    setCurrentQuizData(null);
    setAllPhaseResults([]);
    setTotalScore(0);
  }

  function toggleLanguage() {
    setLanguage((prev) => (prev === "en" ? "hi" : "en"));
  }

  // Render logic based on game state
  if (!started) {
    return <Agreement onStart={handleStart} />;
  }

  if (gameState === "gender") {
    return <GenderSelection />;
  }

  if (gameState === "instruction") {
    return <PhaseInstruction />;
  }

  if (gameState === "quiz") {
    return (
      <QuizGame
        questions={currentQuizData}
        language={language}
        onLanguageToggle={toggleLanguage}
        onQuizEnd={handleQuizEnd}
        phaseTitle={phaseInfo[currentPhase].title}
        userInitials={userInitials}
        currentPhase={currentPhase}
      />
    );
  }

  if (gameState === "results") {
    return <QuizResults />;
  }

  // Gender Selection Component
  function GenderSelection() {
    const [selectedOption, setSelectedOption] = useState(null);

    function handleOptionSelect(option) {
      setSelectedOption(option);
    }

    function handleNext() {
      if (!selectedOption) return;
      handleGenderSelect(selectedOption.text.en.toLowerCase());
    }

    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 p-4 sm:p-6">
        <div className="max-w-2xl mx-auto">
          <div className="bg-black bg-opacity-60 backdrop-blur-lg rounded-xl sm:rounded-2xl p-4 sm:p-8 border border-red-500 border-opacity-30">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 sm:mb-8">
              <h2 className="text-xl sm:text-2xl font-bold text-red-400 mb-4 sm:mb-0">{genderQuestion.question[language]}</h2>
              <button
                onClick={toggleLanguage}
                className="text-xs sm:text-sm px-3 sm:px-4 py-2 bg-red-900 bg-opacity-50 text-red-300 rounded-lg hover:bg-opacity-70 transition-all touch-manipulation self-start sm:self-auto"
              >
                {language === "en" ? "हिंग्लिश" : "English"}
              </button>
            </div>

            <div className="space-y-3 sm:space-y-4">
              {genderQuestion.options.map((option, idx) => (
                <button
                  key={idx}
                  onClick={() => handleOptionSelect(option)}
                  className={`block w-full text-left p-3 sm:p-4 border-2 rounded-lg sm:rounded-xl transition-all duration-300 touch-manipulation ${
                    selectedOption === option
                      ? "bg-gradient-to-r from-red-600 to-red-800 text-white border-red-400 shadow-lg transform scale-105"
                      : "bg-black bg-opacity-40 text-red-300 border-red-500 border-opacity-30 hover:bg-opacity-60 hover:border-opacity-50 active:bg-opacity-70"
                  }`}
                >
                  <span className="text-base sm:text-lg">{option.text[language]}</span>
                </button>
              ))}
            </div>

            {selectedOption && (
              <button
                onClick={handleNext}
                className="mt-6 sm:mt-8 w-full bg-gradient-to-r from-red-600 to-red-800 text-white font-bold py-3 sm:py-4 rounded-lg sm:rounded-xl hover:from-red-700 hover:to-red-900 transition-all duration-300 transform hover:scale-105 shadow-lg touch-manipulation text-sm sm:text-base"
              >
                {language === "en" ? "Continue" : "Aage Badhiye"}
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Phase Instruction Component
  function PhaseInstruction() {
    const currentPhaseInfo = phaseInfo[currentPhase];

    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 p-4 sm:p-6">
        <div className="max-w-3xl mx-auto">
          <div className="bg-black bg-opacity-60 backdrop-blur-lg rounded-xl sm:rounded-2xl p-4 sm:p-8 border border-red-500 border-opacity-30">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 sm:mb-8">
              <div className="flex items-center mb-4 sm:mb-0">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-red-600 to-red-800 rounded-full flex items-center justify-center mr-3 sm:mr-4">
                  <span className="text-lg sm:text-2xl font-bold text-white">{currentPhase}</span>
                </div>
                <h1 className="text-xl sm:text-3xl font-bold text-red-400 leading-tight">
                  {currentPhaseInfo.title[language]}
                </h1>
              </div>
              <button
                onClick={toggleLanguage}
                className="text-xs sm:text-sm px-3 sm:px-4 py-2 bg-red-900 bg-opacity-50 text-red-300 rounded-lg hover:bg-opacity-70 transition-all touch-manipulation self-start sm:self-auto"
              >
                {language === "en" ? "हिंग्लिश" : "English"}
              </button>
            </div>

            <div className="text-center mb-6 sm:mb-8">
              <p className="text-lg sm:text-xl text-red-300 mb-4 sm:mb-6 leading-relaxed">
                {language === "en" ? `Welcome ${userInitials}!` : `Swagat hai ${userInitials}!`}
              </p>

              <div className="bg-red-900 bg-opacity-20 rounded-xl p-4 sm:p-6 mb-4 sm:mb-6">
                <p className="text-sm sm:text-lg text-gray-300 mb-3 sm:mb-4 leading-relaxed">
                  {currentPhaseInfo.description[language]}
                </p>
                <p className="text-red-400 font-medium text-sm sm:text-base">
                  {currentPhaseInfo.instruction[language]}
                </p>
              </div>
            </div>

            <button
              onClick={handleStartPhase}
              className="w-full bg-gradient-to-r from-red-600 to-red-800 text-white font-bold py-3 sm:py-4 rounded-lg sm:rounded-xl hover:from-red-700 hover:to-red-900 transition-all duration-300 transform hover:scale-105 shadow-lg text-base sm:text-xl touch-manipulation"
            >
              {language === "en" ? "Enter Phase" : "Phase Mein Jaiye"}
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Quiz Results Component
  function QuizResults() {
    // Calculate personality
    const personalityData = calculatePersonality(allPhaseResults, selectedGender);
    const personalityResult = getPersonalityResult(personalityData, totalScore, language);

    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 p-4 sm:p-6">
        <div className="max-w-4xl mx-auto">
          <div className="bg-black bg-opacity-60 backdrop-blur-lg rounded-xl sm:rounded-2xl p-4 sm:p-8 border border-red-500 border-opacity-30">

            {/* Header */}
            <div className="text-center mb-6 sm:mb-8">
              <h2 className="text-2xl sm:text-4xl font-bold text-red-400 mb-4">
                {language === "en" ? "All Phases Complete!" : "Sabhi Phases Samapt!"}
              </h2>
              <p className="text-lg sm:text-xl text-red-300 mb-4">
                {language === "en" ? "Thank you" : "Dhanyavaad"}, {userInitials}!
              </p>
            </div>

            {/* Personality Result */}
            <div className="mb-6 sm:mb-8">
              <div className={`bg-gradient-to-r ${personalityResult.color} bg-opacity-20 rounded-xl p-4 sm:p-6 border border-red-400 border-opacity-30`}>
                <div className="text-center mb-4">
                  <div className="text-4xl sm:text-6xl mb-2">{personalityResult.icon}</div>
                  <h3 className="text-xl sm:text-3xl font-bold text-white mb-2">
                    {language === "en" ? "Your Personality:" : "Aapki Personality:"}
                  </h3>
                  <h4 className="text-lg sm:text-2xl font-bold text-red-400 mb-4">
                    {personalityResult.trait}
                  </h4>
                  <p className="text-sm sm:text-base text-red-300 leading-relaxed">
                    {personalityResult.description}
                  </p>
                </div>
              </div>
            </div>

            {/* Score Summary */}
            <div className="mb-6 sm:mb-8">
              <div className="bg-red-900 bg-opacity-20 rounded-xl p-4 sm:p-6">
                <h3 className="text-xl sm:text-2xl font-bold text-red-400 mb-4 text-center">
                  {language === "en" ? "Your Journey Summary:" : "Aapka Safar Summary:"}
                </h3>

                {/* Phase-wise results */}
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 mb-4">
                  {allPhaseResults.map((result, index) => (
                    <div key={index} className="bg-black bg-opacity-40 rounded-lg p-3 sm:p-4">
                      <div className="text-center">
                        <div className="text-red-400 font-bold text-sm sm:text-base">
                          {language === "en" ? `Phase ${result.phase}` : `Phase ${result.phase}`}
                        </div>
                        <div className="text-white text-lg sm:text-xl font-bold">
                          {result.score}
                        </div>
                        <div className="text-red-300 text-xs sm:text-sm">
                          {language === "en" ? "points" : "points"}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="text-center">
                  <p className="text-2xl sm:text-3xl text-white mb-2">
                    {language === "en" ? "Total Score:" : "Kul Score:"}{" "}
                    <span className="text-red-400 font-bold">{totalScore}</span>
                  </p>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-3 sm:space-y-4">
              <button
                onClick={handleRestartGame}
                className="w-full bg-gradient-to-r from-red-600 to-red-800 text-white font-bold py-3 sm:py-4 rounded-lg sm:rounded-xl hover:from-red-700 hover:to-red-900 transition-all duration-300 transform hover:scale-105 shadow-lg touch-manipulation text-sm sm:text-base"
              >
                {language === "en" ? "Start New Game" : "Naya Game Shuru Kariye"}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Default return - should not reach here
  return null;
}
