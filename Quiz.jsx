import React, { useState } from "react";
import {
  genderQuestion,
  maleBranch,
  femaleBranch,
} from "./src/questions";
import Agreement from "./src/Agreement";

// Add image mapping object
const optionImages = {
  // Gender options
  "Male": "/images/male.jpg",
  "Female": "/images/female.jpg",
  
  // Example specific options that need images
  "shuu, its a secret": "/images/secret.jpg",
  "Adventure activity": "/images/adventure.gif",
  "Quiet time together": "/images/quiet-time.gif",
  
  // Add more mappings as needed
};

function shuffleArray(array) {
  return [...array].sort(() => Math.random() - 0.5);
}

export default function Quiz() {
  const [started, setStarted] = useState(false);
  const [userInitials, setUserInitials] = useState("");
  const [language, setLanguage] = useState("en");

  const [currentQuestion, setCurrentQuestion] = useState({
    ...genderQuestion,
    options: shuffleArray(genderQuestion.options),
  });
  const [selectedOption, setSelectedOption] = useState(null);
  const [score, setScore] = useState(0);
  const [answers, setAnswers] = useState([]);
  const [quizEnded, setQuizEnded] = useState(false);

  const [branchData, setBranchData] = useState(null);
  const [unblurredImages, setUnblurredImages] = useState({});

  function handleStart(initials) {
    setUserInitials(initials);
    setStarted(true);
  }

  function handleOptionSelect(option) {
    setSelectedOption(option);
  }

  function handleNext() {
    if (!selectedOption) return;

    setScore((prev) => prev + selectedOption.score);
    setAnswers((prev) => [
      ...prev,
      {
        question: currentQuestion.question[language],
        selected: selectedOption.text[language],
        score: selectedOption.score,
      },
    ]);

    // Gender question logic
    if (currentQuestion.question.en === genderQuestion.question.en) {
      const gender = selectedOption.text.en.toLowerCase();
      const branch = gender === "male" ? maleBranch : femaleBranch;
      setBranchData(branch);

      const nextId = selectedOption.next;
      const nextSet = branch[nextId];
      const randomQ = nextSet[Math.floor(Math.random() * nextSet.length)];

      setCurrentQuestion({
        ...randomQ,
        options: shuffleArray(randomQ.options),
      });
      setSelectedOption(null);
      return;
    }

    const next = selectedOption.next;

    if (next === "end") {
      setQuizEnded(true);
      return;
    }

    const nextQs = branchData[next];
    if (nextQs && nextQs.length > 0) {
      const randomQ = nextQs[Math.floor(Math.random() * nextQs.length)];
      setCurrentQuestion({
        ...randomQ,
        options: shuffleArray(randomQ.options),
      });
      setSelectedOption(null);
    } else {
      setQuizEnded(true);
    }
  }

  function toggleLanguage() {
    setLanguage((prev) => (prev === "en" ? "hi" : "en"));
  }

  function toggleImageBlur(optionText, e) {
    // Stop the event from triggering the parent button click
    e.stopPropagation();
    
    // Toggle the blur state for this specific image
    setUnblurredImages(prev => ({
      ...prev,
      [optionText]: !prev[optionText]
    }));
    
    // Log to verify the function is being called
    console.log("Toggling blur for:", optionText, "New state:", !unblurredImages[optionText]);
  }

  if (!started) {
    return <Agreement onStart={handleStart} />;
  }

  if (quizEnded) {
    return (
      <div className="p-6 max-w-md mx-auto mt-10 bg-white rounded shadow text-center">
        <h2 className="text-2xl font-bold mb-4">
          {language === "en" ? "Quiz Finished!" : "Quiz samapt!"}
        </h2>
        <p className="text-lg">
          {language === "en" ? "Thank you" : "Dhanyavaad"}, {userInitials}!
        </p>
        <p className="text-xl mt-2">
          {language === "en"
            ? "Your total score is:"
            : "Aapka kul score hai:"}{" "}
          {score}
        </p>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-md mx-auto mt-10 bg-white rounded shadow">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold">{currentQuestion.question[language]}</h2>
        <button
          onClick={toggleLanguage}
          className="text-sm px-3 py-1 bg-gray-200 rounded hover:bg-gray-300"
        >
          {language === "en" ? "हिंग्लिश" : "English"}
        </button>
      </div>

      <div className="space-y-3">
        {currentQuestion.options.map((option, idx) => (
          <button
            key={idx}
            onClick={() => handleOptionSelect(option)}
            className={`block w-full text-left p-3 border rounded ${
              selectedOption === option
                ? "bg-blue-500 text-white"
                : "bg-gray-100 hover:bg-gray-200"
            }`}
          >
            <div className="flex items-center">
              {optionImages[option.text.en] && (
                <div className="relative mr-2">
                  <img 
                    src={optionImages[option.text.en]} 
                    alt=""
                    onClick={(e) => toggleImageBlur(option.text.en, e)}
                    className={`w-10 h-10 object-cover rounded cursor-pointer ${
                      !unblurredImages[option.text.en] ? 'filter blur-sm' : ''
                    }`}
                  />
                  {!unblurredImages[option.text.en] && (
                    <div 
                      className="absolute inset-0 flex items-center justify-center text-xs text-white bg-black bg-opacity-30 rounded"
                      onClick={(e) => toggleImageBlur(option.text.en, e)}
                    >
                      Click
                    </div>
                  )}
                </div>
              )}
              <span>{option.text[language]}</span>
            </div>
          </button>
        ))}
      </div>

      {selectedOption && (
        <button
          onClick={handleNext}
          className="mt-6 w-full bg-green-600 text-white py-2 rounded hover:bg-green-700"
        >
          {language === "en" ? "Next" : "Aage"}
        </button>
      )}
    </div>
  );
}
