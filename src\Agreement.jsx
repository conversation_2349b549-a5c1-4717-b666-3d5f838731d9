import React, { useState } from "react";

export default function Agreement({ onStart }) {
  const [initials, setInitials] = useState("");
  const [agreed, setAgreed] = useState(false);

  function handleStartClick() {
    if (!initials.trim()) {
      alert("Please enter your initial (one character)");
      return;
    }
    if (!agreed) {
      alert("You must acknowledge the nature of this experience to proceed.");
      return;
    }
    onStart(initials.trim());
  }

  function handleInitialsChange(e) {
    // Only allow one character
    const value = e.target.value;
    if (value.length <= 1) {
      setInitials(value.toUpperCase());
    }
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-gradient-to-br from-gray-900 via-black to-gray-800 z-50 p-4">
      <div className="bg-black bg-opacity-80 backdrop-blur-lg border border-red-500 border-opacity-30 p-4 sm:p-6 rounded-xl sm:rounded-2xl max-w-sm sm:max-w-md w-full shadow-2xl text-red-300">
        <h2 className="text-xl sm:text-2xl font-bold mb-4 text-red-400 text-center">Important Notice Before You Begin</h2>
        <p className="text-xs sm:text-sm text-red-300 mb-6 leading-relaxed">
          This experience is designed for mature audiences (18+). It contains <strong className="text-red-400">deep psychological questions</strong>,
          <strong className="text-red-400"> moral dilemmas</strong>, and <strong className="text-red-400">thought-provoking scenarios</strong> that may challenge your perspectives.
          Your choices will shape your personality outcome.
        </p>
        <div className="mb-4">
          <label className="block text-red-400 text-sm font-medium mb-2">Enter Your Initial:</label>
          <input
            type="text"
            placeholder="A"
            value={initials}
            onChange={handleInitialsChange}
            maxLength={1}
            className="w-full border-2 border-red-500 border-opacity-50 bg-gray-900 bg-opacity-80 p-3 sm:p-4 rounded-lg text-red-300 placeholder-red-500 placeholder-opacity-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-400 text-center text-2xl sm:text-3xl font-bold touch-manipulation"
          />
        </div>
        <label className="flex items-start mb-6 cursor-pointer select-none touch-manipulation">
          <input
            type="checkbox"
            checked={agreed}
            onChange={(e) => setAgreed(e.target.checked)}
            className="mr-3 mt-1 accent-red-600 w-4 h-4 sm:w-5 sm:h-5"
          />
          <span className="text-xs sm:text-sm text-red-300 leading-relaxed">
            I acknowledge that the questions may be <strong className="text-red-400">challenging</strong> and <strong className="text-red-400">uncomfortable</strong>, and I am participating <strong className="text-red-400">by choice</strong>.
          </span>
        </label>
        <button
          onClick={handleStartClick}
          className="w-full bg-gradient-to-r from-red-600 to-red-800 py-3 sm:py-4 rounded-lg sm:rounded-xl hover:from-red-700 hover:to-red-900 transition-all duration-300 transform hover:scale-105 shadow-lg text-white font-bold text-sm sm:text-base touch-manipulation"
        >
          Begin the Journey
        </button>
      </div>
    </div>
  );
}
