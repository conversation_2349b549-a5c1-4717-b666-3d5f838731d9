import React, { useState } from "react";

export default function Agreement({ onStart }) {
  const [initials, setInitials] = useState("");
  const [agreed, setAgreed] = useState(false);

  function handleStartClick() {
    if (!initials.trim()) {
      alert("Please enter your initials");
      return;
    }
    if (!agreed) {
      alert("You must acknowledge the nature of this experience to proceed.");
      return;
    }
    onStart(initials.trim());
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-90 z-50 p-4">
      <div className="bg-gray-800 p-6 rounded-lg max-w-sm w-full shadow-lg text-pink-300 font-serif">
        <h2 className="text-2xl font-bold mb-4">Important Notice Before You Begin</h2>
        <p className="text-sm text-gray-400 mb-4">
          This experience is designed for mature audiences (18+). It contains **deep psychological questions**, 
          **moral dilemmas**, and **thought-provoking scenarios** that may challenge your perspectives.
          Your choices will shape your personality outcome.
        </p>
        <input
          type="text"
          placeholder="Enter your initials"
          value={initials}
          onChange={(e) => setInitials(e.target.value)}
          className="w-full border border-red-700 bg-gray-900 p-2 rounded mb-4 text-pink-300 placeholder-pink-400 focus:outline-none focus:ring-2 focus:ring-red-700"
        />
        <label className="flex items-center mb-4 cursor-pointer select-none">
          <input
            type="checkbox"
            checked={agreed}
            onChange={(e) => setAgreed(e.target.checked)}
            className="mr-2 accent-red-700"
          />
          I acknowledge that the questions may be **challenging** and **uncomfortable**, and I am participating **by choice**.
        </label>
        <button
          onClick={handleStartClick}
          className="w-full bg-red-700 py-2 rounded hover:bg-yellow-500 hover:text-gray-900 transition duration-300"
        >
          Begin the Journey
        </button>
      </div>
    </div>
  );
}
