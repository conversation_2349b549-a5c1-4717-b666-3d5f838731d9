// Personality Scoring System for QKIS Game

export const personalityTraits = {
  boldSubmissive: {
    name: {
      en: "Bold and Submissive",
      hi: "Bold aur Submissive"
    },
    description: {
      en: "You are confident in expressing your desires but prefer to let others take the lead. You're adventurous yet comfortable following someone else's guidance.",
      hi: "Aap apni ichhaon ko express karne mein confident hain lekin dusron ko lead karne dena pasand karte hain. Aap adventurous hain lekin kisi aur ki guidance follow karna comfortable lagta hai."
    },
    icon: "🔥💫",
    color: "from-orange-500 to-red-600"
  },
  softSubmissive: {
    name: {
      en: "Soft and Submissive", 
      hi: "Soft aur Submissive"
    },
    description: {
      en: "You have a gentle, caring nature and prefer emotional connections over physical ones. You enjoy being cared for and guided by someone you trust.",
      hi: "Aapka nature gentle aur caring hai aur aap physical se zyada emotional connections pasand karte hain. Aap kisi trusted person se care aur guidance lena enjoy karte hain."
    },
    icon: "🌸💕",
    color: "from-pink-400 to-rose-500"
  },
  desperate: {
    name: {
      en: "Desperate",
      hi: "Desperate"
    },
    description: {
      en: "You have intense desires and aren't afraid to pursue what you want. You're passionate and direct about your needs and feelings.",
      hi: "Aapki desires intense hain aur aap jo chahte hain uske liye pursue karne se nahi darte. Aap apni needs aur feelings ke baare mein passionate aur direct hain."
    },
    icon: "🔥💯",
    color: "from-red-600 to-red-800"
  },
  effortlessConfident: {
    name: {
      en: "Effortless/Confident",
      hi: "Effortless/Confident"
    },
    description: {
      en: "You're naturally confident and comfortable with yourself. You don't need to try hard to get what you want - things come naturally to you.",
      hi: "Aap naturally confident hain aur apne saath comfortable hain. Aapko jo chahiye uske liye zyada try nahi karna padta - cheezein naturally aap tak aati hain."
    },
    icon: "👑✨",
    color: "from-purple-500 to-indigo-600"
  }
};

// Scoring weights for different question types
const scoringWeights = {
  emotional: 1.0,
  experience: 1.2,
  provocative: 1.5
};

// Calculate personality based on all phase results
export function calculatePersonality(allPhaseResults, gender) {
  const scores = {
    boldSubmissive: 0,
    softSubmissive: 0,
    desperate: 0,
    effortlessConfident: 0
  };

  // Analyze each phase
  allPhaseResults.forEach((phaseResult, phaseIndex) => {
    const phaseType = phaseIndex === 0 ? 'emotional' : phaseIndex === 1 ? 'experience' : 'provocative';
    const weight = scoringWeights[phaseType];

    phaseResult.answers.forEach(answer => {
      // Analyze answer patterns
      analyzeAnswer(answer, scores, weight, gender, phaseType);
    });
  });

  // Find dominant personality trait
  const dominantTrait = Object.keys(scores).reduce((a, b) => 
    scores[a] > scores[b] ? a : b
  );

  return {
    dominantTrait,
    scores,
    personalityInfo: personalityTraits[dominantTrait]
  };
}

// Analyze individual answers for personality traits
function analyzeAnswer(answer, scores, weight, gender, phaseType) {
  const questionText = answer.question.toLowerCase();
  const selectedText = answer.selected.toLowerCase();
  const score = answer.score;

  // Emotional phase analysis
  if (phaseType === 'emotional') {
    // Relationship status questions
    if (questionText.includes('relationship status')) {
      if (selectedText.includes('exploring') || selectedText.includes('mingle')) {
        scores.effortlessConfident += 2 * weight;
      } else if (selectedText.includes('situationship')) {
        scores.softSubmissive += 1 * weight;
      }
    }

    // Love frequency questions
    if (questionText.includes('fall in love') || questionText.includes('pyaar')) {
      if (selectedText.includes('never') || selectedText.includes('kabhi nahi')) {
        scores.effortlessConfident += 2 * weight;
      } else if (selectedText.includes('multiple') || selectedText.includes('kai baar')) {
        scores.desperate += 2 * weight;
      } else {
        scores.softSubmissive += 1 * weight;
      }
    }

    // Public affection questions
    if (questionText.includes('public affection') || questionText.includes('public mein')) {
      if (selectedText.includes('yes') || selectedText.includes('haan')) {
        scores.boldSubmissive += 2 * weight;
      } else {
        scores.softSubmissive += 1 * weight;
      }
    }
  }

  // Experience phase analysis
  if (phaseType === 'experience') {
    // Virginity questions
    if (questionText.includes('virgin')) {
      if (selectedText.includes('happily no') || selectedText.includes('khushi se nahi')) {
        scores.effortlessConfident += 3 * weight;
      } else if (selectedText.includes('everything except') || selectedText.includes('sab kiya')) {
        scores.boldSubmissive += 2 * weight;
      } else if (selectedText.includes('yes') || selectedText.includes('haan')) {
        scores.softSubmissive += 1 * weight;
      }
    }

    // Physical attraction questions
    if (questionText.includes('physical traits') || questionText.includes('attractive')) {
      if (selectedText.includes('boobs') || selectedText.includes('body shape')) {
        scores.desperate += 2 * weight;
      } else if (selectedText.includes('face') || selectedText.includes('eyes')) {
        scores.softSubmissive += 1 * weight;
      }
    }
  }

  // Provocative phase analysis
  if (phaseType === 'provocative') {
    // Initiation questions
    if (questionText.includes('initiate') || questionText.includes('first')) {
      if (selectedText.includes('definitely me') || selectedText.includes('main')) {
        scores.effortlessConfident += 3 * weight;
      } else if (selectedText.includes('both') || selectedText.includes('dono')) {
        scores.boldSubmissive += 2 * weight;
      } else {
        scores.softSubmissive += 1 * weight;
      }
    }

    // Position preferences
    if (questionText.includes('position') || questionText.includes('desired')) {
      if (selectedText.includes('cowgirl')) {
        scores.effortlessConfident += 3 * weight;
      } else if (selectedText.includes('doggy')) {
        scores.boldSubmissive += 2 * weight;
      } else {
        scores.softSubmissive += 1 * weight;
      }
    }

    // Intensity preferences
    if (questionText.includes('intensity') || questionText.includes('type')) {
      if (selectedText.includes('rough') || selectedText.includes('hard')) {
        scores.desperate += 3 * weight;
      } else if (selectedText.includes('mix') || selectedText.includes('both')) {
        scores.boldSubmissive += 2 * weight;
      } else {
        scores.softSubmissive += 1 * weight;
      }
    }

    // BJ/HJ questions
    if (questionText.includes('bjs') || questionText.includes('hjs')) {
      if (selectedText.includes('love both') || selectedText.includes('dono pasand')) {
        scores.desperate += 3 * weight;
      } else if (selectedText.includes('love it') || selectedText.includes('pasand')) {
        scores.boldSubmissive += 2 * weight;
      } else if (selectedText.includes('hell no') || selectedText.includes('nahi')) {
        scores.softSubmissive += 1 * weight;
      }
    }
  }

  // General score-based analysis
  if (score >= 3) {
    scores.effortlessConfident += 1 * weight;
  } else if (score >= 2) {
    scores.boldSubmissive += 1 * weight;
  } else if (score >= 1) {
    scores.softSubmissive += 1 * weight;
  } else {
    scores.desperate += 0.5 * weight;
  }
}

// Get personality description with score breakdown
export function getPersonalityResult(personalityData, totalScore, language = 'en') {
  const { dominantTrait, scores, personalityInfo } = personalityData;
  
  return {
    trait: personalityInfo.name[language],
    description: personalityInfo.description[language],
    icon: personalityInfo.icon,
    color: personalityInfo.color,
    totalScore,
    breakdown: scores,
    dominantTrait
  };
}
