/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./index.html", "./src/**/*.{js,jsx,ts,tsx}"],
  theme: {
    extend: {
      colors: {
        romanceRed: "#B6364A", // deep warm red
        darkWine: "#3E1F28",    // dark burgundy/wine
        softBlush: "#F5D1C8",   // light blush pink
        nightBlack: "#121212",  // dark background
        warmGold: "#CBA135",    // romantic gold accent
      },
      fontFamily: {
        romance: ["'Playfair Display', serif"], // classic romantic serif
        modern: ["'Montserrat', sans-serif"],   // for clean text
      },
      boxShadow: {
        "romance-glow": "0 0 10px 3px rgba(182, 54, 74, 0.7)", // red glow
      },
    },
  },
  plugins: [],
};
