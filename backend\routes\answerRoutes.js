import express from 'express';
import { saveAnswers, getAllAnswers, getAnswersByInitial } from '../controllers/answerController.js';

const router = express.Router();

// POST /api/answers - Save new quiz answers
router.post('/', saveAnswers);

// GET /api/answers - Get all answers
router.get('/', getAllAnswers);

// GET /api/answers/:initial - Get answers by user initial
router.get('/:initial', getAnswersByInitial);

export default router;