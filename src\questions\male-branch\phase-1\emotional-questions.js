// Phase 1: Emotional and Questioning - Male Branch
// 6 questions focused on emotional topics and general questioning

const emotionalQuestions = [
  {
    id: "m1_1",
    question: {
      en: "What is your current relationship status?",
      hi: "Aapka abhi ka relationship status kya hai?",
    },
    options: [
      { text: { en: "mingle", hi: "mingle" }, score: 2, next: "next" },
      { text: { en: "situationship", hi: "situationship" }, score: 1, next: "next" },
      { text: { en: "im exploring", hi: "exploring" }, score: 3, next: "next" },
    ],
  },
  {
    id: "m1_2",
    question: {
      en: "How many times have you fallen in love?",
      hi: "Aap kitni baar pyaar mein pade hain?",
    },
    options: [
      { text: { en: "Never", hi: "Kabhi nahi" }, score: 1, next: "next" },
      { text: { en: "Once", hi: "Ek baar" }, score: 2, next: "next" },
      { text: { en: "Multiple times", hi: "Kai baar" }, score: 3, next: "next" },
    ],
  },
  {
    id: "m1_3",
    question: {
      en: "Do you enjoy public displays of affection?",
      hi: "Kya aap public mein pyaar dikhana pasand karte hain?",
    },
    options: [
      { text: { en: "Yes", hi: "Haan" }, score: 2, next: "next" },
      { text: { en: "No", hi: "Nahi" }, score: 1, next: "next" },
    ],
  },
  {
    id: "m1_4",
    question: {
      en: "What's your ideal date with a crush?",
      hi: "Crush ke saath ideal date kya hai?",
    },
    options: [
      { text: { en: "Dinner and movie", hi: "Dinner aur movie" }, score: 1, next: "next" },
      { text: { en: "Adventure activity", hi: "Adventure activity" }, score: 2, next: "next" },
      { text: { en: "Quiet time together", hi: "Shanti se saath time" }, score: 3, next: "next" },
    ],
  },
  {
    id: "m1_5",
    question: {
      en: "What do you think makes someone attractive?",
      hi: "Aapke hisaab se kisi ko attractive kya banata hai?",
    },
    options: [
      { text: { en: "Physical appearance", hi: "Shaaririk roop" }, score: 1, next: "next" },
      { text: { en: "Personality", hi: "Vyaktitva" }, score: 2, next: "next" },
      { text: { en: "Intelligence", hi: "Buddhi" }, score: 3, next: "next" },
    ],
  },
  {
    id: "m1_6",
    question: {
      en: "What do you think is the most important aspect of a relationship?",
      hi: "Relationship ka sabse important aspect kya hai?",
    },
    options: [
      { text: { en: "Emotional connection", hi: "Emotional connection" }, score: 2, next: "next" },
      { text: { en: "Physical attraction", hi: "Physical attraction" }, score: 1, next: "next" },
      { text: { en: "Communication and trust", hi: "Communication and trust" }, score: 3, next: "next" },
      { text: { en: "Both", hi: "Dono" }, score: 3, next: "next" },
    ],
  },
];

export default emotionalQuestions;
