import React from 'react';

export default function HeroPage({ selectedGender, onPhaseSelect, language, onLanguageToggle }) {
  const phases = [
    {
      id: 'phase1',
      title: {
        en: 'Phase 1: Emotional and Questioning',
        hi: 'Phase 1: Bhavnatmak aur Sawal'
      },
      description: {
        en: 'Explore emotional topics and general questioning',
        hi: 'Bhavnatmak vishay aur aam sawal'
      },
      color: 'bg-pink-500 hover:bg-pink-600'
    },
    {
      id: 'phase2',
      title: {
        en: 'Phase 2: Your Experience Until Now',
        hi: 'Phase 2: Aapka Ab Tak Ka Anubhav'
      },
      description: {
        en: 'Questions about past experiences and personal history',
        hi: 'Purane anubhav aur vyaktigat itihaas ke sawal'
      },
      color: 'bg-blue-500 hover:bg-blue-600'
    },
    {
      id: 'phase3',
      title: {
        en: 'Phase 3: Will You Do It',
        hi: 'Phase 3: Kya Aap Karenge'
      },
      description: {
        en: 'More provocative questions about willingness to perform actions',
        hi: '<PERSON><PERSON> karne ki ichha ke baare mein sawal'
      },
      color: 'bg-red-500 hover:bg-red-600'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-4xl md:text-6xl font-bold text-white">
              {language === 'en' ? 'QKIS Game' : 'QKIS खेल'}
            </h1>
            <button
              onClick={onLanguageToggle}
              className="text-sm px-4 py-2 bg-white bg-opacity-20 text-white rounded-lg hover:bg-opacity-30 transition-all"
            >
              {language === "en" ? "हिंग्लिश" : "English"}
            </button>
          </div>
          
          <p className="text-xl text-gray-300 mb-4">
            {language === 'en' 
              ? `Welcome ${selectedGender}! Choose your adventure phase:` 
              : `Swagat hai ${selectedGender}! Apna phase chuniye:`
            }
          </p>
          
          <div className="w-24 h-1 bg-gradient-to-r from-pink-500 to-purple-500 mx-auto rounded-full"></div>
        </div>

        {/* Phase Cards */}
        <div className="grid md:grid-cols-1 lg:grid-cols-3 gap-8">
          {phases.map((phase, index) => (
            <div
              key={phase.id}
              className="bg-white bg-opacity-10 backdrop-blur-lg rounded-2xl p-8 border border-white border-opacity-20 hover:bg-opacity-20 transition-all duration-300 transform hover:scale-105"
            >
              <div className="text-center">
                <div className={`w-16 h-16 ${phase.color} rounded-full flex items-center justify-center mx-auto mb-6`}>
                  <span className="text-2xl font-bold text-white">{index + 1}</span>
                </div>
                
                <h2 className="text-2xl font-bold text-white mb-4">
                  {phase.title[language]}
                </h2>
                
                <p className="text-gray-300 mb-8 leading-relaxed">
                  {phase.description[language]}
                </p>
                
                <button
                  onClick={() => onPhaseSelect(phase.id)}
                  className={`w-full ${phase.color} text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg`}
                >
                  {language === 'en' ? 'Enter Phase' : 'Phase Mein Jaiye'}
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Footer */}
        <div className="text-center mt-16">
          <p className="text-gray-400 text-sm">
            {language === 'en' 
              ? 'Each phase contains 6 carefully crafted questions' 
              : 'Har phase mein 6 khaas sawal hain'
            }
          </p>
        </div>
      </div>
    </div>
  );
}
