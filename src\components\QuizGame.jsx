import React, { useState, useEffect } from "react";

// Add image mapping object
const optionImages = {
  // Gender options (non-sensitive)
  "Male": "/images/male.jpg",
  "Female": "/images/female.jpg",

  // Relationship and dating images (non-sensitive)
  "shuu, its a secret": "/images/secret.jpg",
  "Adventure activity": "/images/adventure.gif",
  "Quiet time together": "/images/quiet-time.gif",
  "A beautiful kiss": "/images/kiss.jpg",
  "Kisses and touches": "/images/romantic.jpg",

  // Body part related options (sensitive content)
  "Boobs and ass": "/images/sensitive/boobs.jpg",
  "boobs and ass": "/images/sensitive/boobs.jpg",
  "Boobs": "/images/sensitive/boobs.jpg",
  "boobs": "/images/sensitive/boobs.jpg",

  // Position related (very sensitive)
  "Missionary": "/images/sensitive/missionary.jpg",
  "Doggy style": "/images/sensitive/doggy.jpg",
  "Cowgirl": "/images/sensitive/cowgirl.jpg",

  // Add more mappings as needed
};

// Define which images require content warnings
const sensitiveImages = [
  "Boobs and ass",
  "boobs and ass",
  "Boobs",
  "boobs",
  "Missionary",
  "Doggy style",
  "Cowgirl",
  "I love bjs",
  "I love hjs",
  "I love both",
  "I loved it"
];

function shuffleArray(array) {
  return [...array].sort(() => Math.random() - 0.5);
}

export default function QuizGame({
  questions,
  language,
  onLanguageToggle,
  onQuizEnd,
  phaseTitle,
  userInitials,
  currentPhase
}) {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedOption, setSelectedOption] = useState(null);
  const [score, setScore] = useState(0);
  const [answers, setAnswers] = useState([]);
  const [shuffledQuestions, setShuffledQuestions] = useState([]);

  // Image modal states
  const [showImageModal, setShowImageModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [selectedImageText, setSelectedImageText] = useState("");
  const [showContentWarning, setShowContentWarning] = useState(true);
  const [imageConfirmed, setImageConfirmed] = useState(false);
  const [imageZoomed, setImageZoomed] = useState(false);

  useEffect(() => {
    // Shuffle questions and their options when component mounts
    const shuffled = questions.map(q => ({
      ...q,
      options: shuffleArray(q.options)
    }));
    setShuffledQuestions(shuffled);
  }, [questions]);

  // Handle ESC key to close modal
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === 'Escape' && showImageModal) {
        closeImageModal();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [showImageModal]);

  const currentQuestion = shuffledQuestions[currentQuestionIndex];

  function handleOptionSelect(option) {
    setSelectedOption(option);
  }

  function handleNext() {
    if (!selectedOption) return;

    setScore((prev) => prev + selectedOption.score);
    setAnswers((prev) => [
      ...prev,
      {
        question: currentQuestion.question[language],
        selected: selectedOption.text[language],
        score: selectedOption.score,
      },
    ]);

    // Check if this is the last question
    if (currentQuestionIndex >= shuffledQuestions.length - 1) {
      // Quiz ended
      onQuizEnd({
        score: score + selectedOption.score,
        answers: [...answers, {
          question: currentQuestion.question[language],
          selected: selectedOption.text[language],
          score: selectedOption.score,
        }]
      });
      return;
    }

    // Move to next question
    setCurrentQuestionIndex(prev => prev + 1);
    setSelectedOption(null);
  }

  function handleImageClick(optionText, e) {
    // Stop the event from triggering the parent button click
    e.stopPropagation();

    // Check if this image requires a content warning
    const isSensitive = sensitiveImages.includes(optionText);

    // Open image modal
    setSelectedImage(optionImages[optionText]);
    setSelectedImageText(optionText);
    setShowImageModal(true);
    setShowContentWarning(isSensitive); // Only show warning for sensitive content
    setImageConfirmed(!isSensitive); // Auto-confirm for non-sensitive content
    setImageZoomed(false);
  }

  function handleImageConfirm() {
    setShowContentWarning(false);
    setImageConfirmed(true);
  }

  function closeImageModal() {
    setShowImageModal(false);
    setSelectedImage(null);
    setSelectedImageText("");
    setShowContentWarning(true);
    setImageConfirmed(false);
    setImageZoomed(false);
  }

  function toggleImageZoom() {
    setImageZoomed(!imageZoomed);
  }

  if (!currentQuestion) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 p-6 flex items-center justify-center">
        <div className="bg-black bg-opacity-60 backdrop-blur-lg rounded-2xl p-8 border border-red-500 border-opacity-30 text-center">
          <p className="text-red-400 text-lg">Loading questions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 p-6">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="bg-black bg-opacity-60 backdrop-blur-lg rounded-2xl p-6 mb-6 border border-red-500 border-opacity-30">
          <div className="flex justify-between items-center mb-4">
            <div>
              <div className="flex items-center mb-2">
                <div className="w-8 h-8 bg-gradient-to-r from-red-600 to-red-800 rounded-full flex items-center justify-center mr-3">
                  <span className="text-sm font-bold text-white">{currentPhase}</span>
                </div>
                <h2 className="text-xl font-bold text-red-400">{phaseTitle[language]}</h2>
              </div>
              <p className="text-red-300 text-sm">
                {language === 'en' ? 'Question' : 'Sawal'} {currentQuestionIndex + 1} / {shuffledQuestions.length}
              </p>
            </div>
            <button
              onClick={onLanguageToggle}
              className="text-sm px-4 py-2 bg-red-900 bg-opacity-50 text-red-300 rounded-lg hover:bg-opacity-70 transition-all"
            >
              {language === "en" ? "हिंग्लिश" : "English"}
            </button>
          </div>

          {/* Progress Bar */}
          <div className="w-full bg-gray-800 rounded-full h-3">
            <div
              className="bg-gradient-to-r from-red-600 to-red-800 h-3 rounded-full transition-all duration-300"
              style={{ width: `${((currentQuestionIndex + 1) / shuffledQuestions.length) * 100}%` }}
            ></div>
          </div>
        </div>

        {/* Question Card */}
        <div className="bg-black bg-opacity-60 backdrop-blur-lg rounded-2xl p-8 border border-red-500 border-opacity-30">
          <h3 className="text-2xl font-bold text-red-400 mb-8 leading-relaxed">
            {currentQuestion.question[language]}
          </h3>

          <div className="space-y-4">
            {currentQuestion.options.map((option, idx) => (
              <button
                key={idx}
                onClick={() => handleOptionSelect(option)}
                className={`block w-full text-left p-4 border-2 rounded-xl transition-all duration-300 ${
                  selectedOption === option
                    ? "bg-gradient-to-r from-red-600 to-red-800 text-white border-red-400 shadow-lg transform scale-105"
                    : "bg-black bg-opacity-40 text-red-300 border-red-500 border-opacity-30 hover:bg-opacity-60 hover:border-opacity-50"
                }`}
              >
                <div className="flex items-center">
                  {optionImages[option.text.en] && (
                    <div className="relative mr-4">
                      <div
                        className="w-28 h-20 bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg cursor-pointer border-2 border-red-500 border-opacity-40 hover:border-opacity-100 hover:shadow-xl hover:shadow-red-500/20 transition-all duration-300 flex items-center justify-center group overflow-hidden relative"
                        onClick={(e) => handleImageClick(option.text.en, e)}
                      >
                        <div className="text-center p-3">
                          <div className="text-red-400 text-lg mb-1 group-hover:text-red-300 transition-colors">
                            🖼️
                          </div>
                          <div className="text-red-300 text-xs group-hover:text-white leading-tight font-medium">
                            {language === "en" ? "View Image" : "तस्वीर देखें"}
                          </div>
                        </div>

                        {/* Hover overlay */}
                        <div className="absolute inset-0 bg-gradient-to-br from-red-600 to-red-800 bg-opacity-0 group-hover:bg-opacity-15 transition-all duration-300 rounded-lg"></div>

                        {/* Box border effect */}
                        <div className="absolute inset-0 rounded-lg border border-red-400 border-opacity-0 group-hover:border-opacity-30 transition-all duration-300"></div>
                      </div>

                      {/* Sensitive content indicator - only for sensitive images */}
                      {sensitiveImages.includes(option.text.en) && (
                        <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-600 rounded-full flex items-center justify-center border-2 border-gray-900">
                          <span className="text-white text-xs">⚠</span>
                        </div>
                      )}
                    </div>
                  )}
                  <span className="text-lg">{option.text[language]}</span>
                </div>
              </button>
            ))}
          </div>

          {selectedOption && (
            <button
              onClick={handleNext}
              className="mt-8 w-full bg-gradient-to-r from-red-600 to-red-800 text-white font-bold py-4 rounded-xl hover:from-red-700 hover:to-red-900 transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              {currentQuestionIndex >= shuffledQuestions.length - 1
                ? (language === "en" ? "Complete Phase" : "Phase Samapt")
                : (language === "en" ? "Next" : "Aage")
              }
            </button>
          )}
        </div>
      </div>

      {/* Image Modal */}
      {showImageModal && (
        <div
          className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4"
          onClick={closeImageModal}
        >
          <div
            className="relative max-w-4xl max-h-full w-full"
            onClick={(e) => e.stopPropagation()}
          >
            {showContentWarning && !imageConfirmed ? (
              // Content Warning Screen
              <div className="bg-black bg-opacity-80 backdrop-blur-lg rounded-2xl p-8 border border-red-500 border-opacity-50 text-center">
                <div className="mb-6">
                  <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-white text-2xl font-bold">⚠</span>
                  </div>
                  <h3 className="text-2xl font-bold text-red-400 mb-4">
                    {language === "en" ? "Sensitive Content Warning" : "Sensitive Content Warning"}
                  </h3>
                  <p className="text-red-300 mb-6 leading-relaxed">
                    {language === "en"
                      ? "This image contains mature or adult content. By proceeding, you confirm that you are 18+ and consent to viewing such material."
                      : "Is image mein mature ya adult content hai. Aage badhkar aap confirm karte hain ki aap 18+ hain aur aise material dekhne ke liye consent dete hain."
                    }
                  </p>
                </div>

                <div className="flex gap-4 justify-center">
                  <button
                    onClick={closeImageModal}
                    className="px-6 py-3 bg-gray-600 text-white rounded-xl hover:bg-gray-700 transition-all duration-300"
                  >
                    {language === "en" ? "Cancel" : "Cancel"}
                  </button>
                  <button
                    onClick={handleImageConfirm}
                    className="px-6 py-3 bg-gradient-to-r from-red-600 to-red-800 text-white rounded-xl hover:from-red-700 hover:to-red-900 transition-all duration-300"
                  >
                    {language === "en" ? "I Understand, Continue" : "Main Samjha, Aage Badhiye"}
                  </button>
                </div>
              </div>
            ) : (
              // Image Display Screen
              <div className="bg-black bg-opacity-90 backdrop-blur-lg rounded-2xl p-6 border border-red-500 border-opacity-50 max-w-5xl">
                <div className="flex justify-between items-center mb-4">
                  <div>
                    <h3 className="text-xl font-bold text-red-400">
                      {language === "en" ? "Image Preview" : "Image Preview"}
                    </h3>
                    <p className="text-red-300 text-sm">
                      {selectedImageText}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={toggleImageZoom}
                      className="w-10 h-10 bg-red-600 rounded-full flex items-center justify-center text-white hover:bg-red-700 transition-all duration-300"
                      title={imageZoomed ? "Zoom Out" : "Zoom In"}
                    >
                      {imageZoomed ? "−" : "+"}
                    </button>
                    <button
                      onClick={closeImageModal}
                      className="w-10 h-10 bg-red-600 rounded-full flex items-center justify-center text-white hover:bg-red-700 transition-all duration-300"
                    >
                      ✕
                    </button>
                  </div>
                </div>

                <div className="flex justify-center overflow-auto max-h-96">
                  <img
                    src={selectedImage}
                    alt="Content preview"
                    className={`object-contain rounded-lg cursor-pointer transition-all duration-300 ${
                      imageZoomed
                        ? "max-w-none max-h-none w-auto h-auto"
                        : "max-w-full max-h-80"
                    }`}
                    onClick={toggleImageZoom}
                  />
                </div>

                <div className="mt-4 text-center">
                  <div className="flex justify-center gap-4 mb-2">
                    <button
                      onClick={toggleImageZoom}
                      className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-all duration-300 text-sm"
                    >
                      {imageZoomed
                        ? (language === "en" ? "Zoom Out" : "Zoom Out")
                        : (language === "en" ? "Zoom In" : "Zoom In")
                      }
                    </button>
                  </div>
                  <p className="text-red-300 text-xs">
                    {language === "en"
                      ? "Click image to zoom • ESC or X to close"
                      : "Image par click karke zoom kariye • ESC ya X se close kariye"
                    }
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
