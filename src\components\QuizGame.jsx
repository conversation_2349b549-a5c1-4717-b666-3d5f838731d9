import React, { useState, useEffect } from "react";

// Add image mapping object
const optionImages = {
  // Gender options (non-sensitive)
  "Male": "/images/male.jpg",
  "Female": "/images/female.jpg",

  // Relationship and dating images (non-sensitive)
  "shuu, its a secret": "/images/secret.jpg",
  "Adventure activity": "/images/adventure.gif",
  "Quiet time together": "/images/quiet-time.gif",
  "A beautiful kiss": "/images/kiss.jpg",
  "Kisses and touches": "/images/romantic.jpg",

  // Body part related options (sensitive content)
  "Boobs and ass": "/images/sensitive/boobs.jpg",
  "boobs and ass": "/images/sensitive/boobs.jpg",
  "Boobs": "/images/sensitive/boobs.jpg",
  "boobs": "/images/sensitive/boobs.jpg",

  // Position related (very sensitive)
  "Missionary": "/images/sensitive/missionary.jpg",
  "Doggy style": "/images/sensitive/doggy.jpg",
  "Cowgirl": "/images/sensitive/cowgirl.jpg",

  // Add more mappings as needed
};

// Define which images require content warnings
const sensitiveImages = [
  "Boobs and ass",
  "boobs and ass",
  "Boobs",
  "boobs",
  "Missionary",
  "Doggy style",
  "Cowgirl",
  "I love bjs",
  "I love hjs",
  "I love both",
  "I loved it"
];

function shuffleArray(array) {
  return [...array].sort(() => Math.random() - 0.5);
}

export default function QuizGame({
  questions,
  language,
  onLanguageToggle,
  onQuizEnd,
  phaseTitle,
  userInitials,
  currentPhase
}) {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedOption, setSelectedOption] = useState(null);
  const [score, setScore] = useState(0);
  const [answers, setAnswers] = useState([]);
  const [shuffledQuestions, setShuffledQuestions] = useState([]);

  // Image modal states
  const [showImageModal, setShowImageModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [selectedImageText, setSelectedImageText] = useState("");
  const [showContentWarning, setShowContentWarning] = useState(true);
  const [imageConfirmed, setImageConfirmed] = useState(false);

  useEffect(() => {
    // Shuffle questions and their options when component mounts
    const shuffled = questions.map(q => ({
      ...q,
      options: shuffleArray(q.options)
    }));
    setShuffledQuestions(shuffled);
  }, [questions]);

  // Handle ESC key to close modal
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === 'Escape' && showImageModal) {
        closeImageModal();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [showImageModal]);

  const currentQuestion = shuffledQuestions[currentQuestionIndex];

  function handleOptionSelect(option) {
    setSelectedOption(option);
  }

  function handleNext() {
    if (!selectedOption) return;

    setScore((prev) => prev + selectedOption.score);
    setAnswers((prev) => [
      ...prev,
      {
        question: currentQuestion.question[language],
        selected: selectedOption.text[language],
        score: selectedOption.score,
      },
    ]);

    // Check if this is the last question
    if (currentQuestionIndex >= shuffledQuestions.length - 1) {
      // Quiz ended
      onQuizEnd({
        score: score + selectedOption.score,
        answers: [...answers, {
          question: currentQuestion.question[language],
          selected: selectedOption.text[language],
          score: selectedOption.score,
        }]
      });
      return;
    }

    // Move to next question
    setCurrentQuestionIndex(prev => prev + 1);
    setSelectedOption(null);
  }

  function handleImageClick(optionText, e) {
    // Stop the event from triggering the parent button click
    e.stopPropagation();

    // Check if this image requires a content warning
    const isSensitive = sensitiveImages.includes(optionText);

    // Open image modal
    setSelectedImage(optionImages[optionText]);
    setSelectedImageText(optionText);
    setShowImageModal(true);
    setShowContentWarning(isSensitive); // Only show warning for sensitive content
    setImageConfirmed(!isSensitive); // Auto-confirm for non-sensitive content
  }

  function handleImageConfirm() {
    setShowContentWarning(false);
    setImageConfirmed(true);
  }

  function closeImageModal() {
    setShowImageModal(false);
    setSelectedImage(null);
    setSelectedImageText("");
    setShowContentWarning(true);
    setImageConfirmed(false);
  }

  if (!currentQuestion) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 p-6 flex items-center justify-center">
        <div className="bg-black bg-opacity-60 backdrop-blur-lg rounded-2xl p-8 border border-red-500 border-opacity-30 text-center">
          <p className="text-red-400 text-lg">Loading questions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 p-4 sm:p-6">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="bg-black bg-opacity-60 backdrop-blur-lg rounded-xl sm:rounded-2xl p-4 sm:p-6 mb-4 sm:mb-6 border border-red-500 border-opacity-30">
          <div className="flex justify-between items-start sm:items-center mb-4">
            <div className="flex-1">
              <div className="flex items-center mb-2">
                <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-red-600 to-red-800 rounded-full flex items-center justify-center mr-2 sm:mr-3">
                  <span className="text-xs sm:text-sm font-bold text-white">{currentPhase}</span>
                </div>
                <h2 className="text-lg sm:text-xl font-bold text-red-400 leading-tight">{phaseTitle[language]}</h2>
              </div>
              <p className="text-red-300 text-xs sm:text-sm">
                {language === 'en' ? 'Question' : 'Sawal'} {currentQuestionIndex + 1} / {shuffledQuestions.length}
              </p>
            </div>
            <button
              onClick={onLanguageToggle}
              className="text-xs sm:text-sm px-3 sm:px-4 py-2 bg-red-900 bg-opacity-50 text-red-300 rounded-lg hover:bg-opacity-70 transition-all touch-manipulation ml-2"
            >
              {language === "en" ? "हिंग्लिश" : "English"}
            </button>
          </div>

          {/* Progress Bar */}
          <div className="w-full bg-gray-800 rounded-full h-2 sm:h-3">
            <div
              className="bg-gradient-to-r from-red-600 to-red-800 h-2 sm:h-3 rounded-full transition-all duration-300"
              style={{ width: `${((currentQuestionIndex + 1) / shuffledQuestions.length) * 100}%` }}
            ></div>
          </div>
        </div>

        {/* Question Card */}
        <div className="bg-black bg-opacity-60 backdrop-blur-lg rounded-xl sm:rounded-2xl p-4 sm:p-8 border border-red-500 border-opacity-30">
          <h3 className="text-lg sm:text-2xl font-bold text-red-400 mb-6 sm:mb-8 leading-relaxed">
            {currentQuestion.question[language]}
          </h3>

          <div className="space-y-3 sm:space-y-4">
            {currentQuestion.options.map((option, idx) => (
              <button
                key={idx}
                onClick={() => handleOptionSelect(option)}
                className={`block w-full text-left p-3 sm:p-4 border-2 rounded-lg sm:rounded-xl transition-all duration-300 touch-manipulation ${
                  selectedOption === option
                    ? "bg-gradient-to-r from-red-600 to-red-800 text-white border-red-400 shadow-lg transform scale-105"
                    : "bg-black bg-opacity-40 text-red-300 border-red-500 border-opacity-30 hover:bg-opacity-60 hover:border-opacity-50 active:bg-opacity-70"
                }`}
              >
                <div className="flex items-center">
                  {optionImages[option.text.en] && (
                    <div className="relative mr-3 sm:mr-4">
                      <div
                        className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg cursor-pointer border-2 border-red-500 border-opacity-40 hover:border-opacity-100 hover:shadow-xl hover:shadow-red-500/20 transition-all duration-300 flex items-center justify-center group overflow-hidden relative touch-manipulation"
                        onClick={(e) => handleImageClick(option.text.en, e)}
                      >
                        <div className="text-center p-1 sm:p-2">
                          <div className="text-red-400 text-sm sm:text-base mb-1 group-hover:text-red-300 transition-colors">
                            🖼️
                          </div>
                          <div className="text-red-300 text-xs group-hover:text-white leading-tight font-medium">
                            {language === "en" ? "Preview" : "प्रीव्यू"}
                          </div>
                        </div>

                        {/* Hover overlay */}
                        <div className="absolute inset-0 bg-gradient-to-br from-red-600 to-red-800 bg-opacity-0 group-hover:bg-opacity-15 transition-all duration-300 rounded-lg"></div>

                        {/* Box border effect */}
                        <div className="absolute inset-0 rounded-lg border border-red-400 border-opacity-0 group-hover:border-opacity-30 transition-all duration-300"></div>
                      </div>

                      {/* Sensitive content indicator - only for sensitive images */}
                      {sensitiveImages.includes(option.text.en) && (
                        <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-600 rounded-full flex items-center justify-center border-2 border-gray-900">
                          <span className="text-white text-xs">⚠</span>
                        </div>
                      )}
                    </div>
                  )}
                  <span className="text-sm sm:text-lg flex-1">{option.text[language]}</span>
                </div>
              </button>
            ))}
          </div>

          {selectedOption && (
            <button
              onClick={handleNext}
              className="mt-6 sm:mt-8 w-full bg-gradient-to-r from-red-600 to-red-800 text-white font-bold py-3 sm:py-4 rounded-lg sm:rounded-xl hover:from-red-700 hover:to-red-900 transition-all duration-300 transform hover:scale-105 shadow-lg touch-manipulation text-sm sm:text-base"
            >
              {currentQuestionIndex >= shuffledQuestions.length - 1
                ? (language === "en" ? "Complete Phase" : "Phase Samapt")
                : (language === "en" ? "Next" : "Aage")
              }
            </button>
          )}
        </div>
      </div>

      {/* Image Modal */}
      {showImageModal && (
        <div
          className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4"
          onClick={closeImageModal}
        >
          <div
            className="relative max-w-4xl max-h-full w-full"
            onClick={(e) => e.stopPropagation()}
          >
            {showContentWarning && !imageConfirmed ? (
              // Content Warning Screen
              <div className="bg-black bg-opacity-80 backdrop-blur-lg rounded-2xl p-8 border border-red-500 border-opacity-50 text-center">
                <div className="mb-6">
                  <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-white text-2xl font-bold">⚠</span>
                  </div>
                  <h3 className="text-2xl font-bold text-red-400 mb-4">
                    {language === "en" ? "Sensitive Content Warning" : "Sensitive Content Warning"}
                  </h3>
                  <p className="text-red-300 mb-6 leading-relaxed">
                    {language === "en"
                      ? "This image contains mature or adult content. By proceeding, you confirm that you are 18+ and consent to viewing such material."
                      : "Is image mein mature ya adult content hai. Aage badhkar aap confirm karte hain ki aap 18+ hain aur aise material dekhne ke liye consent dete hain."
                    }
                  </p>
                </div>

                <div className="flex gap-4 justify-center">
                  <button
                    onClick={closeImageModal}
                    className="px-6 py-3 bg-gray-600 text-white rounded-xl hover:bg-gray-700 transition-all duration-300"
                  >
                    {language === "en" ? "Cancel" : "Cancel"}
                  </button>
                  <button
                    onClick={handleImageConfirm}
                    className="px-6 py-3 bg-gradient-to-r from-red-600 to-red-800 text-white rounded-xl hover:from-red-700 hover:to-red-900 transition-all duration-300"
                  >
                    {language === "en" ? "I Understand, Continue" : "Main Samjha, Aage Badhiye"}
                  </button>
                </div>
              </div>
            ) : (
              // Image Display Screen
              <div className="bg-black bg-opacity-90 backdrop-blur-lg rounded-xl sm:rounded-2xl p-4 sm:p-6 border border-red-500 border-opacity-50 max-w-sm sm:max-w-2xl mx-4">
                <div className="flex justify-between items-center mb-4">
                  <div>
                    <h3 className="text-lg sm:text-xl font-bold text-red-400">
                      {language === "en" ? "Image Preview" : "Image Preview"}
                    </h3>
                    <p className="text-red-300 text-xs sm:text-sm">
                      {selectedImageText}
                    </p>
                  </div>
                  <button
                    onClick={closeImageModal}
                    className="w-8 h-8 sm:w-10 sm:h-10 bg-red-600 rounded-full flex items-center justify-center text-white hover:bg-red-700 transition-all duration-300 touch-manipulation"
                  >
                    ✕
                  </button>
                </div>

                <div className="flex justify-center">
                  <img
                    src={selectedImage}
                    alt="Content preview"
                    className="max-w-full max-h-64 sm:max-h-80 object-contain rounded-lg"
                  />
                </div>

                <div className="mt-4 text-center">
                  <p className="text-red-300 text-xs sm:text-sm">
                    {language === "en"
                      ? "Tap X or press ESC to close"
                      : "X par tap kariye ya ESC press kariye"
                    }
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
