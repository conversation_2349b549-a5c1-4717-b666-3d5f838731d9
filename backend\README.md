# Quiz Backend

This is the backend server for the quiz application, responsible for storing and retrieving quiz answers in MongoDB.

## Setup

1. Install dependencies:
   ```
   npm install
   ```

2. Create a `.env` file in the root directory with the following variables:
   ```
   PORT=5000
   MONGODB_URI=mongodb://localhost:27017/quiz_answers
   ```
   
   For production, use your MongoDB Atlas connection string:
   ```
   MONGODB_URI=mongodb+srv://<username>:<password>@cluster0.mongodb.net/quiz_answers
   ```

3. Start the server:
   ```
   npm start
   ```
   
   For development with auto-restart:
   ```
   npm run dev
   ```

## API Endpoints

### Save Quiz Answers
- **URL**: `/api/answers`
- **Method**: `POST`
- **Body**:
  ```json
  {
    "userInitial": "A",
    "answers": [
      {
        "question": "What is your gender?",
        "selected": "Male",
        "score": 0
      },
      {
        "question": "What is your favorite sport?",
        "selected": "Football",
        "score": 2
      }
    ],
    "totalScore": 2
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "message": "Answers saved successfully",
    "data": {
      "_id": "60f8a1b3e6b3f32a4c8b4567",
      "userInitial": "A",
      "answers": [...],
      "totalScore": 2,
      "timestamp": "2023-07-21T12:34:56.789Z"
    }
  }
  ```

### Get All Answers
- **URL**: `/api/answers`
- **Method**: `GET`
- **Response**:
  ```json
  {
    "success": true,
    "count": 2,
    "data": [
      {
        "_id": "60f8a1b3e6b3f32a4c8b4567",
        "userInitial": "A",
        "answers": [...],
        "totalScore": 2,
        "timestamp": "2023-07-21T12:34:56.789Z"
      },
      {
        "_id": "60f8a1b3e6b3f32a4c8b4568",
        "userInitial": "B",
        "answers": [...],
        "totalScore": 5,
        "timestamp": "2023-07-21T12:35:56.789Z"
      }
    ]
  }
  ```

### Get Answers by User Initial
- **URL**: `/api/answers/:initial`
- **Method**: `GET`
- **Response**:
  ```json
  {
    "success": true,
    "count": 1,
    "data": [
      {
        "_id": "60f8a1b3e6b3f32a4c8b4567",
        "userInitial": "A",
        "answers": [...],
        "totalScore": 2,
        "timestamp": "2023-07-21T12:34:56.789Z"
      }
    ]
  }
  ```