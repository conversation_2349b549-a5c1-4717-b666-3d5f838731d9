import Answer from '../models/Answer.js';

// Save new quiz answers
export const saveAnswers = async (req, res) => {
  try {
    const { userInitial, answers, totalScore } = req.body;
    
    // Validate input
    if (!userInitial || !answers || answers.length === 0 || totalScore === undefined) {
      return res.status(400).json({ message: 'Missing required fields' });
    }
    
    // Create new answer document
    const newAnswer = new Answer({
      userInitial,
      answers,
      totalScore
    });
    
    // Save to database
    await newAnswer.save();
    
    res.status(201).json({ 
      success: true, 
      message: 'Answers saved successfully',
      data: newAnswer
    });
  } catch (error) {
    console.error('Error saving answers:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to save answers',
      error: error.message 
    });
  }
};

// Get all answers
export const getAllAnswers = async (req, res) => {
  try {
    const answers = await Answer.find().sort({ timestamp: -1 });
    res.status(200).json({ 
      success: true, 
      count: answers.length,
      data: answers 
    });
  } catch (error) {
    console.error('Error fetching answers:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch answers',
      error: error.message 
    });
  }
};

// Get answers by user initial
export const getAnswersByInitial = async (req, res) => {
  try {
    const { initial } = req.params;
    
    if (!initial || initial.length !== 1) {
      return res.status(400).json({ message: 'Invalid user initial' });
    }
    
    const answers = await Answer.find({ userInitial: initial }).sort({ timestamp: -1 });
    
    res.status(200).json({ 
      success: true, 
      count: answers.length,
      data: answers 
    });
  } catch (error) {
    console.error('Error fetching answers by initial:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch answers',
      error: error.message 
    });
  }
};